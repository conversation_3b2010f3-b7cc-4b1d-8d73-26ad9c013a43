import * as AtivacaoSuspensao from '../exports';

interface IAtivacaoSuspensaoContribuicaoContext {
  contribuicoes: AtivacaoSuspensao.IContribuicaoItem[];
  loading: boolean;
  toggleContribuicao: (index: number) => void;
}

const AtivacaoSuspensaoContribuicaoContext =
  AtivacaoSuspensao.React.createContext<
    IAtivacaoSuspensaoContribuicaoContext | undefined
  >(undefined);

export const AtivacaoSuspensaoContribuicaoProvider: React.FC<{
  children: AtivacaoSuspensao.ReactNode;
}> = ({ children }) => {
  const formulario = AtivacaoSuspensao.useFormularioAtivacaoSuspensao();

  return (
    <AtivacaoSuspensaoContribuicaoContext.Provider value={formulario}>
      {children}
    </AtivacaoSuspensaoContribuicaoContext.Provider>
  );
};

export const useAtivacaoSuspensaoContribuicaoContext = () => {
  const context = AtivacaoSuspensao.useContext(
    AtivacaoSuspensaoContribuicaoContext,
  );
  if (!context) {
    throw new Error(
      'useAtivacaoSuspensaoContribuicaoContext deve ser usado dentro de AtivacaoSuspensaoContribuicaoProvider',
    );
  }
  return context;
};
