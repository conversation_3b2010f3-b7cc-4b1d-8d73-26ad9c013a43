// React dependencies
export { default as React, useState, useContext, useEffect } from 'react';
export type { ReactNode } from 'react';

// UI Components from design system
export {
  Grid,
  GridItem,
  Text,
  LoadingSpinner,
  Table,
  Button,
  ConditionalRenderer,
  Alert,
  IconInfoRound,
  IconWarningRound,
  IconCheckRound,
  Switch,
  Checkbox,
} from '@cvp/design-system-caixa';

export type { TableColumn } from '@cvp/design-system-caixa/dist/types/table-types';

// Styled Components
export { default as styled } from 'styled-components';

// Utils
export {
  formatarValorPadraoBrasileiro,
  tryGetValueOrDefault,
  checkIfAllItemsAreTrue,
} from '@cvp/utils';

// Components from @cvp/componentes-posvenda
export { For, Match, SwitchCase } from '@cvp/componentes-posvenda';

// Context
export { AtivacaoSuspensaoContribuicaoProvider } from './context/AtivacaoSuspensaoContribuicaoContext';
export { useAtivacaoSuspensaoContribuicaoContext } from './context/AtivacaoSuspensaoContribuicaoContext';

// Components
export { SolicitacaoAtivacaoSuspensao } from './views/SolicitacaoAtivacaoSuspensao';
export { TabelaContribuicoes } from './components/TabelaContribuicoes/TabelaContribuicoes';
export { SecaoConfirmacao } from './components/SecaoConfirmacao';
export { DadosCertificado } from './components/DadosCertificado';

// Hooks
export { useFormularioAtivacaoSuspensao } from './hooks/useFormularioAtivacaoSuspensao';
export { useAssinaturaAtivacaoSuspensao } from './hooks/useAssinaturaAtivacaoSuspensao';
export { useRecuperarContribuicoesCertificado } from './hooks/useRecuperarContribuicoesCertificado';

// Types
export type { IAtivacaoSuspensaoContribuicaoData } from './types/IAtivacaoSuspensaoContribuicaoData';
export type { IContribuicaoItem } from './types/IContribuicaoItem';
export type {
  IRecuperarContribuicoesCertificadoPayload,
  IRecuperarContribuicoesCertificadoResponse,
  IUseRecuperarContribuicoesCertificado,
} from './types/IRecuperarContribuicoesCertificado';

// Constants
export { ATIVACAO_SUSPENSAO_CONSTANTS } from './constants/ativacaoSuspensaoConstants';

// Mocks
export { mockRecuperarContribuicoesCertificado } from './mocks/mockRecuperarContribuicoesCertificado';

// Styles
export {
  AtivacaoSuspensaoTable,
  StatusButton,
  ContainerPrincipal,
  TituloSecao,
  ContribuicaoCard,
  TotalContainer,
} from './styles';

// Factory
export { colunasTabelaContribuicoes } from './factory/colunasTabelaContribuicoes';
