import { IHandleReponseResult } from '@cvp/componentes-posvenda';

export interface IRecuperarContribuicoesCertificadoPayload {
  Cpf: string;
  NumeroCertificado: string;
  categoria: string;
  cpfCnpj: string;
  xToken: string;
}

export interface IBeneficioContribuicaoCertificado {
  [key: string]: any;
}

export interface IRecuperarContribuicoesCertificadoResponse {
  empresaId: string;
  contaId: string;
  nomePessoaCertificado: string;
  cpfPessoaCertificado: string;
  produtoId: string;
  descricaoProduto: string;
  subCategoriaProduto: string;
  produtoAgredado: string;
  valorTotalSaldo: string;
  beneficioContribuicaoCertificado: IBeneficioContribuicaoCertificado[];
}

export interface IUseRecuperarContribuicoesCertificado {
  loading: boolean;
  response: IRecuperarContribuicoesCertificadoResponse | null;
  invocarApiGatewayCvpComToken: (
    dynamicPayload?: Partial<IRecuperarContribuicoesCertificadoPayload>,
  ) => Promise<
    IHandleReponseResult<IRecuperarContribuicoesCertificadoResponse> | undefined
  >;
}
