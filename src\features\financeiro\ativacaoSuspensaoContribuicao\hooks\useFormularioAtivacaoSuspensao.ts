import * as AtivacaoSuspensao from '../exports';

export const useFormularioAtivacaoSuspensao = () => {
  const [contribuicoes, setContribuicoes] = AtivacaoSuspensao.useState<
    AtivacaoSuspensao.IContribuicaoItem[]
  >([]);
  const [loading, setLoading] = AtivacaoSuspensao.useState(false);

  const toggleContribuicao = (index: number) => {
    setContribuicoes((prev: AtivacaoSuspensao.IContribuicaoItem[]) =>
      prev.map((contrib: AtivacaoSuspensao.IContribuicaoItem, i: number) =>
        i === index ? { ...contrib, ativo: !contrib.ativo } : contrib,
      ),
    );
  };

  const carregarContribuicoes = async () => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));

      const dadosExemplo: AtivacaoSuspensao.IContribuicaoItem[] = [
        {
          tipo: 'reserva',
          nome: 'Reserva',
          valorContribuicao: 187.46,
          saldoAcumulado: 36637.4,
          ativo: true,
        },
        {
          tipo: 'cuidadoExtra',
          nome: 'Pecúlio',
          valorContribuicao: 150.0,
          valorIdentizacao: 150.0,
          prazoRecebimento: 'Único',
          ativo: true,
        },
      ];

      setContribuicoes(dadosExemplo);
    } catch (error) {
      console.error(
        AtivacaoSuspensao.ATIVACAO_SUSPENSAO_CONSTANTS.MENSAGENS.ERRO_CARREGAR,
        error,
      );
    } finally {
      setLoading(false);
    }
  };

  AtivacaoSuspensao.useEffect(() => {
    carregarContribuicoes();
  }, []);

  return {
    contribuicoes,
    loading,
    toggleContribuicao,
    carregarContribuicoes,
  };
};
