import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { tryGetValueOrDefault, getSessionItem } from '@cvp/utils';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import {
  IRecuperarContribuicoesCertificadoPayload,
  IRecuperarContribuicoesCertificadoResponse,
  IUseRecuperarContribuicoesCertificado,
} from '../types/IRecuperarContribuicoesCertificado';
import { mockRecuperarContribuicoesCertificado } from '../mocks/mockRecuperarContribuicoesCertificado';

export const useRecuperarContribuicoesCertificado = (
  numeroCertificado?: string,
): IUseRecuperarContribuicoesCertificado => {
  const cpfCnpj = String(getSessionItem<string>('cpfCnpj'));
  const xToken = String(getSessionItem<string>('xToken'));

  const payload: IRecuperarContribuicoesCertificadoPayload = {
    Cpf: cpfCnpj,
    NumeroCertificado: numeroCertificado || '',
    categoria: 'RE',
    cpfCnpj,
    xToken,
  };

  const { loading, response, invocarApiGatewayCvpComToken } =
    useApiGatewayCvpInvoker<
      IRecuperarContribuicoesCertificadoPayload,
      IRecuperarContribuicoesCertificadoResponse
    >(PECOS.RecuperarContribuicoesCertificado, {
      data: payload,
      autoFetch: false,
      cache: true,
      cacheKey: `${cpfCnpj}-${numeroCertificado}`,
    });

  const responseData =
    response?.entidade || mockRecuperarContribuicoesCertificado;

  return {
    loading,
    response: tryGetValueOrDefault(
      [responseData],
      null as IRecuperarContribuicoesCertificadoResponse | null,
    ),
    invocarApiGatewayCvpComToken,
  };
};
